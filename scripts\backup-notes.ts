#!/usr/bin/env tsx

import fs from 'fs/promises';
import path from 'path';
import { execSync } from 'child_process';
import prisma from '../lib/prisma';

interface BackupMetadata {
  timestamp: string;
  version: string;
  notesCount: number;
  filesCount: number;
  totalFileSize: number;
  databaseUrl: string;
}

interface BackupOptions {
  outputDir?: string;
  includeFiles?: boolean;
  includeDatabaseDump?: boolean;
  compress?: boolean;
}

class NotesBackupService {
  private backupDir: string;
  private uploadsDir: string;

  constructor(backupDir: string = './backups') {
    this.backupDir = backupDir;
    this.uploadsDir = path.join(process.cwd(), 'uploads');
  }

  /**
   * Create a complete backup of notes and uploaded files
   */
  async createBackup(options: BackupOptions = {}): Promise<string> {
    const {
      outputDir = this.backupDir,
      includeFiles = true,
      includeDatabaseDump = true,
      compress = true
    } = options;

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupName = `notes-backup-${timestamp}`;
    const backupPath = path.join(outputDir, backupName);

    console.log(`🚀 Starting backup: ${backupName}`);
    console.log(`📁 Backup location: ${backupPath}`);

    try {
      // Ensure backup directory exists
      await fs.mkdir(backupPath, { recursive: true });

      // 1. Export notes data from database
      console.log('📊 Exporting notes data from database...');
      const notesData = await this.exportNotesData();
      await fs.writeFile(
        path.join(backupPath, 'notes-data.json'),
        JSON.stringify(notesData, null, 2)
      );

      // 2. Create database dump if requested
      if (includeDatabaseDump) {
        console.log('💾 Creating database dump...');
        await this.createDatabaseDump(backupPath);
      }

      // 3. Copy uploaded files if requested
      let filesCount = 0;
      let totalFileSize = 0;
      if (includeFiles) {
        console.log('📁 Copying uploaded files...');
        const fileStats = await this.copyUploadedFiles(backupPath);
        filesCount = fileStats.count;
        totalFileSize = fileStats.totalSize;
      }

      // 4. Create backup metadata
      const metadata: BackupMetadata = {
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        notesCount: notesData.notes.length,
        filesCount,
        totalFileSize,
        databaseUrl: process.env.DATABASE_URL?.replace(/\/\/.*@/, '//***:***@') || 'unknown'
      };

      await fs.writeFile(
        path.join(backupPath, 'backup-metadata.json'),
        JSON.stringify(metadata, null, 2)
      );

      // 5. Compress backup if requested
      if (compress) {
        console.log('🗜️  Compressing backup...');
        const archivePath = await this.compressBackup(backupPath);
        
        // Remove uncompressed directory
        await fs.rm(backupPath, { recursive: true });
        
        console.log(`✅ Backup completed successfully!`);
        console.log(`📦 Archive: ${archivePath}`);
        console.log(`📊 Notes: ${metadata.notesCount}, Files: ${metadata.filesCount}`);
        console.log(`💾 Total size: ${this.formatBytes(metadata.totalFileSize)}`);
        
        return archivePath;
      }

      console.log(`✅ Backup completed successfully!`);
      console.log(`📁 Backup directory: ${backupPath}`);
      console.log(`📊 Notes: ${metadata.notesCount}, Files: ${metadata.filesCount}`);
      console.log(`💾 Total size: ${this.formatBytes(metadata.totalFileSize)}`);

      return backupPath;

    } catch (error) {
      console.error('❌ Backup failed:', error);
      
      // Cleanup on failure
      try {
        await fs.rm(backupPath, { recursive: true });
      } catch (cleanupError) {
        console.error('Failed to cleanup backup directory:', cleanupError);
      }
      
      throw error;
    }
  }

  /**
   * Export notes data and related information from database
   */
  private async exportNotesData() {
    console.log('  📋 Fetching notes from database...');
    
    const notes = await prisma.note.findMany({
      include: {
        year: true,
        subject: true,
        unit: true,
        generationBatches: {
          include: {
            questions: {
              select: {
                id: true,
                questionId: true,
                type: true,
                promptEn: true,
                promptZh: true,
                promptMs: true,
                tpLevel: true,
                status: true
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'asc'
      }
    });

    // Also get related data for context
    const years = await prisma.year.findMany({ orderBy: { yearNumber: 'asc' } });
    const subjects = await prisma.subject.findMany({ orderBy: { name: 'asc' } });
    const units = await prisma.unit.findMany({ 
      orderBy: [{ yearId: 'asc' }, { subjectId: 'asc' }, { unitNumber: 'asc' }]
    });

    console.log(`  ✅ Found ${notes.length} notes`);

    return {
      notes,
      years,
      subjects,
      units,
      exportedAt: new Date().toISOString(),
      exportVersion: '1.0.0'
    };
  }

  /**
   * Create a PostgreSQL database dump
   */
  private async createDatabaseDump(backupPath: string): Promise<void> {
    const databaseUrl = process.env.DATABASE_URL;
    if (!databaseUrl) {
      throw new Error('DATABASE_URL environment variable not set');
    }

    // Parse database URL to extract connection details
    const url = new URL(databaseUrl);
    const host = url.hostname;
    const port = url.port || '5432';
    const database = url.pathname.slice(1);
    const username = url.username;
    const password = url.password;

    const dumpFile = path.join(backupPath, 'database-dump.sql');

    try {
      // Set PGPASSWORD environment variable for pg_dump
      const env = { ...process.env, PGPASSWORD: password };
      
      const command = `pg_dump -h ${host} -p ${port} -U ${username} -d ${database} --no-password --verbose`;
      
      console.log(`  🔧 Running: pg_dump -h ${host} -p ${port} -U ${username} -d ${database}`);
      
      const output = execSync(command, { 
        env,
        encoding: 'utf8',
        maxBuffer: 1024 * 1024 * 100 // 100MB buffer
      });
      
      await fs.writeFile(dumpFile, output);
      console.log(`  ✅ Database dump created: ${dumpFile}`);
      
    } catch (error) {
      console.error('  ❌ Failed to create database dump:', error);
      throw new Error(`Database dump failed: ${error}`);
    }
  }

  /**
   * Copy uploaded files to backup directory
   */
  private async copyUploadedFiles(backupPath: string): Promise<{ count: number; totalSize: number }> {
    const filesBackupDir = path.join(backupPath, 'uploads');
    await fs.mkdir(filesBackupDir, { recursive: true });

    let count = 0;
    let totalSize = 0;

    try {
      // Check if uploads directory exists
      await fs.access(this.uploadsDir);
      
      const files = await fs.readdir(this.uploadsDir);
      console.log(`  📁 Found ${files.length} files to backup`);

      for (const file of files) {
        const sourcePath = path.join(this.uploadsDir, file);
        const destPath = path.join(filesBackupDir, file);
        
        try {
          const stats = await fs.stat(sourcePath);
          if (stats.isFile()) {
            await fs.copyFile(sourcePath, destPath);
            count++;
            totalSize += stats.size;
            
            if (count % 10 === 0) {
              console.log(`    📄 Copied ${count} files...`);
            }
          }
        } catch (fileError) {
          console.warn(`    ⚠️  Failed to copy file ${file}:`, fileError);
        }
      }

      console.log(`  ✅ Copied ${count} files (${this.formatBytes(totalSize)})`);
      
    } catch (error) {
      if ((error as any).code === 'ENOENT') {
        console.log('  ℹ️  No uploads directory found, skipping file backup');
      } else {
        throw error;
      }
    }

    return { count, totalSize };
  }

  /**
   * Compress backup directory into a tar.gz archive
   */
  private async compressBackup(backupPath: string): Promise<string> {
    const archivePath = `${backupPath}.tar.gz`;
    const backupName = path.basename(backupPath);
    const parentDir = path.dirname(backupPath);

    try {
      // Use tar to create compressed archive
      execSync(`tar -czf "${archivePath}" -C "${parentDir}" "${backupName}"`, {
        stdio: 'inherit'
      });

      console.log(`  ✅ Archive created: ${archivePath}`);
      return archivePath;
      
    } catch (error) {
      console.error('  ❌ Failed to compress backup:', error);
      throw new Error(`Compression failed: ${error}`);
    }
  }

  /**
   * Format bytes to human readable string
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const options: BackupOptions = {};

  // Parse command line arguments
  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--no-files':
        options.includeFiles = false;
        break;
      case '--no-db-dump':
        options.includeDatabaseDump = false;
        break;
      case '--no-compress':
        options.compress = false;
        break;
      case '--output-dir':
        options.outputDir = args[++i];
        break;
      case '--help':
        console.log(`
Usage: npm run backup:notes [options]

Options:
  --no-files        Skip backing up uploaded files
  --no-db-dump      Skip creating database dump
  --no-compress     Don't compress the backup
  --output-dir DIR  Specify output directory (default: ./backups)
  --help           Show this help message

Examples:
  npm run backup:notes
  npm run backup:notes -- --no-compress --output-dir /path/to/backups
  npm run backup:notes -- --no-files
        `);
        process.exit(0);
    }
  }

  try {
    const backupService = new NotesBackupService();
    await backupService.createBackup(options);
    process.exit(0);
  } catch (error) {
    console.error('Backup failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

export { NotesBackupService };
