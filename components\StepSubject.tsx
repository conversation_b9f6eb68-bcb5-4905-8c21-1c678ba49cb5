import React from 'react';
import { Subject } from '../lib/api';

interface StepSubjectProps {
  subjects: Subject[];
  onSelect: (subjectId: number) => void;
  onBack: () => void;
}

const StepSubject: React.FC<StepSubjectProps> = ({ subjects, onSelect, onBack }) => {
  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold">Select Subject</h2>
      
      {subjects.length === 0 ? (
        <p className="text-gray-500">No subjects available.</p>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {subjects.map((subject) => (
            <button
              key={subject.id}
              onClick={() => {
                console.log('Subject selected:', subject.name);
                onSelect(subject.id);
              }}
              className="p-4 border rounded-lg shadow-sm hover:shadow-md transition-shadow bg-gray-50 hover:bg-gray-100"
            >
              <h3 className="text-lg font-medium">{subject.name}</h3>
            </button>
          ))}
        </div>
      )}
      
      <div className="flex justify-between mt-8">
        <button
          onClick={onBack}
          className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          Back
        </button>
      </div>
    </div>
  );
};

export default StepSubject;
