import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../lib/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from './auth/[...nextauth]';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  const { attemptId } = req.query;

  if (!attemptId || typeof attemptId !== 'string') {
    return res.status(400).json({ message: 'Quiz attempt ID is required' });
  }

  try {
    // Get the quiz attempt
    const quizAttempt = await prisma.quizAttempt.findUnique({
      where: { id: Number(attemptId) },
      select: {
        id: true,
        unitId: true,
        quizType: true,
        studentAnswers: true
      }
    });

    if (!quizAttempt) {
      return res.status(404).json({ message: 'Quiz attempt not found' });
    }

    // Get the quiz type directly from the database
    // The quizType field is an enum in the database (MASTERY, TEST, QUICK)
    // Convert it to lowercase for consistency with the frontend
    const quizType = quizAttempt.quizType.toLowerCase();

    // Return the quiz type
    res.status(200).json({ quizType });
  } catch (error) {
    console.error('Error inferring quiz type:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
}
