-- CreateTable
CREATE TABLE "TG_Notes" (
    "id" SERIAL NOT NULL,
    "filename" TEXT NOT NULL,
    "fileUrl" TEXT NOT NULL,
    "mimeType" TEXT NOT NULL,
    "fileSize" INTEGER NOT NULL,
    "yearId" INTEGER NOT NULL,
    "subjectId" INTEGER NOT NULL,
    "unitId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "TG_Notes_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "TG_Notes" ADD CONSTRAINT "TG_Notes_yearId_fkey" FOREIGN KEY ("yearId") REFERENCES "TG_Year"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TG_Notes" ADD CONSTRAINT "TG_Notes_subjectId_fkey" FOREIGN KEY ("subjectId") REFERENCES "TG_Subject"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TG_Notes" ADD CONSTRAINT "TG_Notes_unitId_fkey" FOREIGN KEY ("unitId") REFERENCES "TG_Unit"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
