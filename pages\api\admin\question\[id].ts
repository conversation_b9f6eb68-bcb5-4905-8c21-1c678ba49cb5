import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../../../lib/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  try {
    // Check authentication and admin role
    const session = await getServerSession(req, res, authOptions);

    if (!session) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    if (session.user?.role !== 'ADMIN') {
      return res.status(403).json({ message: 'Forbidden - Admin access required' });
    }

    // Get question ID from the URL
    const { id } = req.query;

    if (!id || Array.isArray(id)) {
      return res.status(400).json({ message: 'Invalid question ID' });
    }

    // Fetch the question with all related data
    const question = await prisma.question.findUnique({
      where: { id: parseInt(id) },
      include: {
        choices: true,
        answer: true,
        explanation: true,
        unit: true,
        subject: true,
        year: true,
        promptMedia: true
      },
    });

    if (!question) {
      return res.status(404).json({ message: 'Question not found' });
    }

    // Return the question data
    res.status(200).json(question);
  } catch (error) {
    console.error('Error fetching question:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
}
