-- CreateEnum
CREATE TYPE "QuestionType" AS ENUM ('MULTIPLE_CHOICE', 'SHORT_ANSWER');

-- CreateTable
CREATE TABLE "TG_Question" (
    "id" SERIAL NOT NULL,
    "questionId" TEXT NOT NULL,
    "type" "QuestionType" NOT NULL,
    "promptEn" TEXT NOT NULL,
    "promptZh" TEXT NOT NULL,
    "subjectId" INTEGER NOT NULL,
    "yearId" INTEGER NOT NULL,
    "unitId" INTEGER NOT NULL,
    "topic" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TG_Question_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TG_Choice" (
    "id" SERIAL NOT NULL,
    "key" TEXT NOT NULL,
    "textEn" TEXT NOT NULL,
    "textZh" TEXT NOT NULL,
    "questionId" INTEGER NOT NULL,

    CONSTRAINT "TG_Choice_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TG_Answer" (
    "id" SERIAL NOT NULL,
    "questionId" INTEGER NOT NULL,
    "key" TEXT,
    "textEn" TEXT,
    "textZh" TEXT,

    CONSTRAINT "TG_Answer_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TG_ExplanationText" (
    "id" SERIAL NOT NULL,
    "questionId" INTEGER NOT NULL,
    "textEn" TEXT NOT NULL,
    "textZh" TEXT NOT NULL,

    CONSTRAINT "TG_ExplanationText_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "TG_Question_questionId_key" ON "TG_Question"("questionId");

-- CreateIndex
CREATE UNIQUE INDEX "TG_Answer_questionId_key" ON "TG_Answer"("questionId");

-- CreateIndex
CREATE UNIQUE INDEX "TG_ExplanationText_questionId_key" ON "TG_ExplanationText"("questionId");

-- AddForeignKey
ALTER TABLE "TG_Question" ADD CONSTRAINT "TG_Question_subjectId_fkey" FOREIGN KEY ("subjectId") REFERENCES "TG_Subject"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TG_Question" ADD CONSTRAINT "TG_Question_yearId_fkey" FOREIGN KEY ("yearId") REFERENCES "TG_Year"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TG_Question" ADD CONSTRAINT "TG_Question_unitId_fkey" FOREIGN KEY ("unitId") REFERENCES "TG_Unit"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TG_Choice" ADD CONSTRAINT "TG_Choice_questionId_fkey" FOREIGN KEY ("questionId") REFERENCES "TG_Question"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TG_Answer" ADD CONSTRAINT "TG_Answer_questionId_fkey" FOREIGN KEY ("questionId") REFERENCES "TG_Question"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TG_ExplanationText" ADD CONSTRAINT "TG_ExplanationText_questionId_fkey" FOREIGN KEY ("questionId") REFERENCES "TG_Question"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
