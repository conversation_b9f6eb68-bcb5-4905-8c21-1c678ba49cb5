import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../lib/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from './auth/[...nextauth]';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  const { childId } = req.query;

  if (!childId || typeof childId !== 'string') {
    return res.status(400).json({ message: 'Child ID is required' });
  }

  try {
    const attempts = await prisma.quizAttempt.findMany({
      where: {
        childId: Number(childId),
        status: 'ACTIVE'
      },
      orderBy: {
        startTime: 'desc'
      },
      include: {
        subject: {
          select: {
            name: true
          }
        },
        unit: {
          select: {
            unitNumber: true,
            topicEn: true,
            topicZh: true,
            topicMs: true
          }
        },
        studentAnswers: {
          select: {
            id: true
          }
        }
      }
    });

    const mapped = attempts.map(a => ({
      id: a.id,
      subject: a.subject.name,
      unitNumber: a.unit?.unitNumber ?? null,
      topic: {
        en: a.unit?.topicEn,
        zh: a.unit?.topicZh,
        ms: a.unit?.topicMs
      },
      // currentQuestionIndex is 0-based and represents the current question the user is on
      // So the number of completed questions is the currentQuestionIndex
      progress: `${a.currentQuestionIndex}/${a.questionIds.length}`,
      // Use the actual quiz type from the database
      quizType: a.quizType ? a.quizType.toLowerCase() : 'test'
    }));

    res.status(200).json(mapped);
  } catch (error) {
    console.error('Error fetching homework:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
}
