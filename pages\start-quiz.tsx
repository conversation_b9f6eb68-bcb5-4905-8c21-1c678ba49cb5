import { NextPage } from 'next';
import Head from 'next/head';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import StartQuiz from '../components/StartQuiz';

const StartQuizPage: NextPage = () => {
  const { status } = useSession();
  const router = useRouter();

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  return (
    <>
      <Head>
        <title>Start Quiz | My Quiz App</title>
        <meta name="description" content="Start a new quiz" />
      </Head>

      <main className="min-h-screen bg-gray-50">
        <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
          <StartQuiz />
        </div>
      </main>
    </>
  );
};

export default StartQuizPage;
