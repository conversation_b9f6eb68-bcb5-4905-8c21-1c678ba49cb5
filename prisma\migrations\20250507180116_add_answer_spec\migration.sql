/*
  Warnings:

  - You are about to drop the column `submittedAnswer` on the `TG_StudentAnswer` table. All the data in the column will be lost.

*/
-- CreateEnum
CREATE TYPE "AnswerType" AS ENUM ('SINGLE_CHOICE', 'MULTI_CHOICE', 'SHORT_TEXT', 'TRUE_FALSE', 'FILL_IN_THE_BLANK', 'MATCHING', 'SEQUENCING', 'LONG_TEXT_RUBRIC');

-- AlterTable
ALTER TABLE "TG_Answer" ADD COLUMN     "answerSpec" JSONB,
ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "type" "AnswerType" NOT NULL DEFAULT 'SINGLE_CHOICE';

-- AlterTable
ALTER TABLE "TG_StudentAnswer" DROP COLUMN "submittedAnswer",
ADD COLUMN     "submittedJson" JSONB,
ADD COLUMN     "submittedKey" TEXT,
ADD COLUMN     "submittedText" TEXT;
