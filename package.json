{"name": "my-quiz-app", "version": "1.0.0", "scripts": {"dev": "next dev", "dev:custom": "node server.js", "build": "next build", "start": "node server.js", "seed": "ts-node prisma/seed.ts", "lang:seed": "ts-node scripts/set-language-default.ts", "seed:history": "ts-node scripts/seed-history-units.ts", "verify:history": "ts-node scripts/verify-history-units.ts", "verify:rbt": "ts-node scripts/verify-rbt-units.ts", "verify:subjects": "ts-node scripts/verify-subjects.ts", "backfill:answer": "tsx scripts/backfill-answer-type.ts", "test": "jest", "test:coverage": "jest --coverage", "test:grading": "jest test/grading.test.ts", "test:backup": "jest test/backup-restore.test.ts", "create:admin": "ts-node scripts/create-admin-user.ts", "worker": "tsx scripts/questionWorker.ts", "backup:notes": "tsx scripts/backup-notes.ts", "restore:notes": "tsx scripts/restore-notes.ts"}, "jest": {"preset": "ts-jest", "testEnvironment": "jsdom", "setupFilesAfterEnv": ["./jest.setup.js"]}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@modelcontextprotocol/server-postgres": "^0.6.2", "@prisma/client": "^6.6.0", "@tailwindcss/line-clamp": "^0.4.4", "@types/uuid": "^10.0.0", "argon2": "^0.43.0", "dayjs": "^1.11.13", "express": "^5.1.0", "formidable": "^3.5.4", "lodash": "^4.17.21", "lucide-react": "^0.510.0", "next": "^15.3.1", "next-auth": "^4.24.7", "node-fetch": "^3.3.2", "pinyin": "^4.0.0-alpha.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-highlight-words": "^0.21.0", "react-markdown": "^10.1.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "segmentit": "^2.0.3", "swr": "^2.3.3", "uuid": "^11.1.0"}, "devDependencies": {"@jest/globals": "^29.7.0", "@testing-library/jest-dom": "^6.6.3", "@types/axios": "^0.14.4", "@types/express": "^5.0.1", "@types/formidable": "^3.4.5", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.16", "@types/node": "22.14.1", "@types/react": "19.1.2", "autoprefixer": "^10.4.0", "axios": "^1.9.0", "eslint": "9.26.0", "eslint-config-next": "15.3.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-environment-node": "^29.7.0", "node-mocks-http": "^1.16.2", "postcss": "^8.4.0", "prisma": "^6.6.0", "supertest": "^7.1.0", "tailwindcss": "^3.3.0", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.0.0"}}