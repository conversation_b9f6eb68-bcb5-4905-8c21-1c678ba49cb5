/*
  Warnings:

  - You are about to drop the column `parentId` on the `TG_Account` table. All the data in the column will be lost.
  - You are about to drop the column `parentId` on the `TG_Child` table. All the data in the column will be lost.
  - You are about to drop the `TG_Parent` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `name` to the `TG_Account` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "TG_Account" DROP CONSTRAINT "TG_Account_parentId_fkey";

-- DropForeignKey
ALTER TABLE "TG_Child" DROP CONSTRAINT "TG_Child_parentId_fkey";

-- DropIndex
DROP INDEX "TG_Child_pin_key";

-- AlterTable
ALTER TABLE "TG_Account" DROP COLUMN "parentId",
ADD COLUMN "name" TEXT;

-- Update existing rows with placeholder values
UPDATE "TG_Account" SET "name" = 'Placeholder Name' WHERE "name" IS NULL;

-- AlterTable to make the column NOT NULL
ALTER TABLE "TG_Account" ALTER COLUMN "name" SET NOT NULL;

-- AlterTable
ALTER TABLE "TG_Child" DROP COLUMN "parentId";

-- DropTable
DROP TABLE "TG_Parent";
