# Notes Backup and Restore System

This document describes the backup and restore system for uploaded notes and quiz question generation files in the quiz application.

## Overview

The backup system creates comprehensive backups that include:
- **Database records**: All notes metadata from the `TG_Notes` table and related data
- **Uploaded files**: All files stored in the `uploads` directory
- **Metadata**: Backup information including timestamps, counts, and version info
- **Database dump**: Complete PostgreSQL dump for full recovery (optional)

## Quick Start

### Creating a Backup

```bash
# Create a complete backup (recommended)
npm run backup:notes

# Create backup without compression
npm run backup:notes -- --no-compress

# Create backup without uploaded files (database only)
npm run backup:notes -- --no-files

# Create backup to specific directory
npm run backup:notes -- --output-dir /path/to/backups
```

### Restoring from Backup

```bash
# Restore from compressed backup
npm run restore:notes backup.tar.gz

# Restore from directory
npm run restore:notes ./backups/notes-backup-2024-01-15T10-30-00-000Z

# Dry run (see what would be restored)
npm run restore:notes backup.tar.gz -- --dry-run

# Restore database only (skip files)
npm run restore:notes backup.tar.gz -- --no-files

# Overwrite existing records/files
npm run restore:notes backup.tar.gz -- --overwrite
```

## Backup Structure

Each backup contains the following structure:

```
notes-backup-2024-01-15T10-30-00-000Z/
├── backup-metadata.json      # Backup information and statistics
├── notes-data.json          # Notes records and related data
├── database-dump.sql        # Complete PostgreSQL dump (optional)
└── uploads/                 # Copy of all uploaded files
    ├── file1.pdf
    ├── file2.jpg
    └── ...
```

### Backup Metadata

The `backup-metadata.json` file contains:

```json
{
  "timestamp": "2024-01-15T10:30:00.000Z",
  "version": "1.0.0",
  "notesCount": 150,
  "filesCount": 145,
  "totalFileSize": 52428800,
  "databaseUrl": "postgresql://***:***@localhost:5433/education"
}
```

### Notes Data

The `notes-data.json` file contains:

```json
{
  "notes": [...],           // Array of note records with full details
  "years": [...],           // Related year records
  "subjects": [...],        // Related subject records  
  "units": [...],           // Related unit records
  "exportedAt": "2024-01-15T10:30:00.000Z",
  "exportVersion": "1.0.0"
}
```

## Command Reference

### Backup Command

```bash
npm run backup:notes [options]
```

**Options:**
- `--no-files`: Skip backing up uploaded files
- `--no-db-dump`: Skip creating database dump
- `--no-compress`: Don't compress the backup
- `--output-dir DIR`: Specify output directory (default: `./backups`)
- `--help`: Show help message

**Examples:**
```bash
# Full backup with compression
npm run backup:notes

# Backup without files, no compression
npm run backup:notes -- --no-files --no-compress

# Backup to custom location
npm run backup:notes -- --output-dir /mnt/backup-drive
```

### Restore Command

```bash
npm run restore:notes <backup-path> [options]
```

**Arguments:**
- `backup-path`: Path to backup directory or `.tar.gz` file

**Options:**
- `--no-files`: Skip restoring uploaded files
- `--no-database`: Skip restoring database records
- `--overwrite`: Overwrite existing records/files (default: skip existing)
- `--dry-run`: Show what would be restored without making changes
- `--help`: Show help message

**Examples:**
```bash
# Restore from compressed backup
npm run restore:notes ./backups/notes-backup-2024-01-15T10-30-00-000Z.tar.gz

# Dry run to see what would be restored
npm run restore:notes backup.tar.gz -- --dry-run

# Restore only database records
npm run restore:notes backup.tar.gz -- --no-files

# Force overwrite existing data
npm run restore:notes backup.tar.gz -- --overwrite
```

## Environment Requirements

### Prerequisites

1. **Node.js and npm**: Required for running the scripts
2. **PostgreSQL client tools**: Required for database dumps
   - Install `postgresql-client` on Ubuntu/Debian
   - Install `postgresql` on macOS via Homebrew
   - Ensure `pg_dump` is available in PATH

3. **tar**: Required for compression (usually pre-installed on Unix systems)

### Environment Variables

The following environment variables must be set:

```bash
DATABASE_URL="postgresql://username:password@host:port/database"
```

## Storage Considerations

### Backup Size Estimation

- **Database records**: Usually small (< 1MB for thousands of notes)
- **Uploaded files**: Varies greatly depending on file types and sizes
- **Database dump**: Can be large for databases with many questions/answers
- **Compression**: Typically reduces size by 60-80% for mixed content

### Retention Policy

Consider implementing a retention policy:

```bash
# Keep last 30 days of backups
find ./backups -name "notes-backup-*.tar.gz" -mtime +30 -delete

# Keep last 10 backups
ls -t ./backups/notes-backup-*.tar.gz | tail -n +11 | xargs rm -f
```

## Automation

### Scheduled Backups

Add to crontab for automated backups:

```bash
# Daily backup at 2 AM
0 2 * * * cd /path/to/quiz-app && npm run backup:notes

# Weekly backup with cleanup
0 2 * * 0 cd /path/to/quiz-app && npm run backup:notes && find ./backups -name "*.tar.gz" -mtime +30 -delete
```

### Backup Verification

Always verify backups after creation:

```bash
# Test restore in dry-run mode
npm run restore:notes latest-backup.tar.gz -- --dry-run

# Check backup integrity
tar -tzf backup.tar.gz > /dev/null && echo "Archive OK" || echo "Archive corrupted"
```

## Troubleshooting

### Common Issues

1. **Permission denied on uploads directory**
   ```bash
   sudo chown -R $USER:$USER uploads/
   chmod -R 755 uploads/
   ```

2. **pg_dump not found**
   ```bash
   # Ubuntu/Debian
   sudo apt-get install postgresql-client
   
   # macOS
   brew install postgresql
   ```

3. **Database connection failed**
   - Verify `DATABASE_URL` environment variable
   - Check database server is running
   - Verify network connectivity and credentials

4. **Insufficient disk space**
   - Check available space: `df -h`
   - Clean old backups: `rm old-backup.tar.gz`
   - Use `--no-compress` to avoid temporary space usage

### Recovery Scenarios

**Scenario 1: Lost uploaded files**
```bash
npm run restore:notes backup.tar.gz -- --no-database
```

**Scenario 2: Corrupted database**
```bash
# Restore from database dump
psql $DATABASE_URL < backup/database-dump.sql

# Or restore notes data only
npm run restore:notes backup.tar.gz -- --no-files
```

**Scenario 3: Complete system recovery**
```bash
# Full restore
npm run restore:notes backup.tar.gz -- --overwrite
```

## Security Considerations

1. **Backup encryption**: Consider encrypting backups containing sensitive data
2. **Access control**: Restrict access to backup files and directories
3. **Network security**: Use secure protocols when transferring backups
4. **Database credentials**: Ensure `DATABASE_URL` is properly secured

## Integration with Existing Systems

The backup system integrates with:
- **Docker Compose**: Uses existing database configuration
- **Prisma ORM**: Leverages existing database models
- **Storage Service**: Works with the existing file storage abstraction
- **Environment Configuration**: Uses existing environment variable setup

## Future Enhancements

Potential improvements:
- Cloud storage integration (S3, Azure Blob, Google Cloud)
- Incremental backups
- Backup encryption
- Web UI for backup management
- Backup verification and integrity checks
- Automated backup scheduling within the application
