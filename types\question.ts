import { QuestionType } from '@prisma/client';

/**
 * Specification for fill-in-the-blank questions
 */
export type FillBlankSpec = {
  template: string;
  blanks: {
    correct: string;
    distractors: string[];
  }[];
  caseSensitive?: boolean;
};

/**
 * Specification for matching questions
 */
export type MatchingSpec = {
  pairs: {
    left: string;
    leftZh?: string;
    leftMs?: string;
    right?: string;
    rightZh?: string;
    rightMs?: string;
    rightImage?: string;
  }[];
  shuffleLeft?: boolean;
  shuffleRight?: boolean;
};

/**
 * Specification for sequencing questions
 */
export type SequencingSpec = {
  items: (string | {
    textEn: string;
    textZh?: string;
    textMs?: string;
  })[];
  correctOrder: number[];
  allowPartialCredit?: boolean;
};

/**
 * Specification for long answer questions
 */
export type LongAnswerSpec = {
  prompt: string;
  keyPoints: string[];
  minSimilarity: number;
  maxWords?: number;
};

/**
 * Map of question types to their specific spec types
 */
export type QuestionSpecMap = {
  MULTIPLE_CHOICE: undefined;
  MULTIPLE_CHOICE_IMAGE: {
    shuffleChoices?: boolean;
    choiceImageStyle?: 'square' | 'circle' | 'thumbnail';
  };
  PICTURE_PROMPT: {
    altTextEn?: string;
    altTextZh?: string;
    license?: string;
  };
  FILL_IN_THE_BLANK: FillBlankSpec;
  TRUE_FALSE: {
    statement: string;
    correctAnswer: boolean;
  };
  SHORT_ANSWER: {
    acceptableAnswers: string[];
    maxChars?: number;
    caseSensitive?: boolean;
  };
  LONG_ANSWER: LongAnswerSpec;
  MATCHING: MatchingSpec;
  SEQUENCING: SequencingSpec;
};

/**
 * Type for a question spec based on the question type
 */
export type QuestionSpec<T extends QuestionType> = T extends keyof QuestionSpecMap
  ? QuestionSpecMap[T]
  : never;

/**
 * Type for a question with its spec
 */
export interface QuestionWithSpec {
  id: number;
  type: QuestionType;
  spec: any; // Will be cast to the appropriate type based on question type
}

/**
 * Helper function to get the typed spec for a question
 */
export function getTypedSpec<T extends QuestionType>(
  question: QuestionWithSpec,
  type: T
): QuestionSpecMap[T] | null {
  if (question.type !== type) {
    return null;
  }
  return question.spec as QuestionSpecMap[T];
}
