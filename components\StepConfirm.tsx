import React from 'react';
import { QuizFormState } from './StartQuiz';

interface StepConfirmProps {
  formData: QuizFormState;
  subjectName: string;
  topicName: string;
  onConfirm: () => void;
  onBack: () => void;
}

const StepConfirm: React.FC<StepConfirmProps> = ({
  formData,
  subjectName,
  topicName,
  onConfirm,
  onBack,
}) => {
  const getModeLabel = (mode: string | null) => {
    switch (mode) {
      case 'mastery':
        return 'Mastery Quiz';
      case 'test':
        return 'Test Quiz';
      case 'quick':
        return 'Quick Quiz';
      default:
        return 'Unknown';
    }
  };

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold">Confirm Quiz Settings</h2>
      
      <div className="bg-gray-50 p-6 rounded-lg border">
        <div className="space-y-4">
          <div>
            <h3 className="text-sm font-medium text-gray-500">Quiz Mode</h3>
            <p className="mt-1 text-lg">{getModeLabel(formData.mode)}</p>
          </div>
          
          <div>
            <h3 className="text-sm font-medium text-gray-500">Subject</h3>
            <p className="mt-1 text-lg">{subjectName}</p>
          </div>
          
          <div>
            <h3 className="text-sm font-medium text-gray-500">Topic</h3>
            <p className="mt-1 text-lg">
              {formData.autoSelectTopic ? 'Auto-selected from subject' : topicName}
            </p>
          </div>
        </div>
      </div>
      
      <div className="flex justify-between mt-8">
        <button
          onClick={onBack}
          className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          Back
        </button>
        
        <button
          onClick={() => {
            console.log('Quiz confirmed with settings:', {
              mode: formData.mode,
              subject: subjectName,
              topic: topicName,
              autoSelect: formData.autoSelectTopic,
            });
            onConfirm();
          }}
          className="px-6 py-3 bg-blue-600 text-white rounded-md shadow-sm text-sm font-medium hover:bg-blue-700"
        >
          Start Quiz
        </button>
      </div>
    </div>
  );
};

export default StepConfirm;
