#!/usr/bin/env tsx

/**
 * Example script demonstrating how to use the backup and restore system
 * This script shows various backup scenarios and usage patterns
 */

import { NotesBackupService } from './backup-notes';
import { NotesRestoreService } from './restore-notes';
import path from 'path';

async function demonstrateBackupSystem() {
  console.log('🎯 Backup and Restore System Demo\n');

  const backupService = new NotesBackupService();
  const restoreService = new NotesRestoreService();

  try {
    // Example 1: Create a full backup
    console.log('📦 Example 1: Creating a full backup...');
    const fullBackup = await backupService.createBackup({
      includeFiles: true,
      includeDatabaseDump: true,
      compress: true
    });
    console.log(`✅ Full backup created: ${fullBackup}\n`);

    // Example 2: Create a database-only backup
    console.log('📊 Example 2: Creating database-only backup...');
    const dbOnlyBackup = await backupService.createBackup({
      includeFiles: false,
      includeDatabaseDump: true,
      compress: false
    });
    console.log(`✅ Database backup created: ${dbOnlyBackup}\n`);

    // Example 3: Create a files-only backup
    console.log('📁 Example 3: Creating files-only backup...');
    const filesOnlyBackup = await backupService.createBackup({
      includeFiles: true,
      includeDatabaseDump: false,
      compress: false
    });
    console.log(`✅ Files backup created: ${filesOnlyBackup}\n`);

    // Example 4: Demonstrate restore dry run
    console.log('🧪 Example 4: Testing restore (dry run)...');
    await restoreService.restoreBackup({
      backupPath: fullBackup,
      restoreFiles: true,
      restoreDatabase: true,
      dryRun: true
    });
    console.log('✅ Dry run completed successfully\n');

    // Example 5: Show backup information
    console.log('📋 Example 5: Backup information summary...');
    await showBackupInfo(fullBackup);

  } catch (error) {
    console.error('❌ Demo failed:', error);
  }
}

async function showBackupInfo(backupPath: string) {
  const fs = await import('fs/promises');
  
  try {
    let actualPath = backupPath;
    
    // If it's a compressed file, we need to extract it first to read metadata
    if (backupPath.endsWith('.tar.gz')) {
      console.log('  📦 Compressed backup detected');
      // For demo purposes, we'll just show the file size
      const stats = await fs.stat(backupPath);
      console.log(`  💾 Archive size: ${formatBytes(stats.size)}`);
      return;
    }

    // Read metadata
    const metadataPath = path.join(actualPath, 'backup-metadata.json');
    const metadataContent = await fs.readFile(metadataPath, 'utf8');
    const metadata = JSON.parse(metadataContent);

    console.log('  📊 Backup Statistics:');
    console.log(`    📅 Created: ${new Date(metadata.timestamp).toLocaleString()}`);
    console.log(`    📝 Notes: ${metadata.notesCount}`);
    console.log(`    📁 Files: ${metadata.filesCount}`);
    console.log(`    💾 Total size: ${formatBytes(metadata.totalFileSize)}`);
    console.log(`    🔧 Version: ${metadata.version}`);

  } catch (error) {
    console.log('  ⚠️  Could not read backup metadata:', error);
  }
}

function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Example usage scenarios
async function usageExamples() {
  console.log('\n🎓 Common Usage Scenarios:\n');

  console.log('1. Daily automated backup:');
  console.log('   npm run backup:notes');
  console.log('   # Creates compressed backup with all data\n');

  console.log('2. Quick database backup before updates:');
  console.log('   npm run backup:notes -- --no-files --no-compress');
  console.log('   # Fast backup of just database records\n');

  console.log('3. Restore after data loss:');
  console.log('   npm run restore:notes backup.tar.gz');
  console.log('   # Restores everything, skips existing files\n');

  console.log('4. Preview restore without changes:');
  console.log('   npm run restore:notes backup.tar.gz -- --dry-run');
  console.log('   # Shows what would be restored\n');

  console.log('5. Force overwrite during restore:');
  console.log('   npm run restore:notes backup.tar.gz -- --overwrite');
  console.log('   # Overwrites existing files and records\n');

  console.log('6. Restore only files (keep current database):');
  console.log('   npm run restore:notes backup.tar.gz -- --no-database');
  console.log('   # Restores only uploaded files\n');
}

// Backup best practices
async function bestPractices() {
  console.log('\n💡 Best Practices:\n');

  console.log('🔄 Regular Backups:');
  console.log('  - Schedule daily backups via cron');
  console.log('  - Keep multiple backup versions');
  console.log('  - Test restore process regularly\n');

  console.log('🗂️  Storage Management:');
  console.log('  - Store backups on separate drives/servers');
  console.log('  - Implement backup retention policy');
  console.log('  - Monitor backup sizes and success\n');

  console.log('🔒 Security:');
  console.log('  - Encrypt backups containing sensitive data');
  console.log('  - Secure backup storage locations');
  console.log('  - Limit access to backup files\n');

  console.log('⚡ Performance:');
  console.log('  - Use --no-db-dump for faster backups');
  console.log('  - Consider --no-files for database-only backups');
  console.log('  - Schedule backups during low-usage periods\n');
}

// Troubleshooting guide
async function troubleshooting() {
  console.log('\n🔧 Troubleshooting Common Issues:\n');

  console.log('❌ "pg_dump not found":');
  console.log('  Solution: Install PostgreSQL client tools');
  console.log('  Ubuntu: sudo apt-get install postgresql-client');
  console.log('  macOS: brew install postgresql\n');

  console.log('❌ "Permission denied" on uploads:');
  console.log('  Solution: Fix file permissions');
  console.log('  sudo chown -R $USER:$USER uploads/');
  console.log('  chmod -R 755 uploads/\n');

  console.log('❌ "Database connection failed":');
  console.log('  Solution: Check DATABASE_URL and database status');
  console.log('  Verify: echo $DATABASE_URL');
  console.log('  Test: psql $DATABASE_URL\n');

  console.log('❌ "Insufficient disk space":');
  console.log('  Solution: Free up space or use different location');
  console.log('  Check: df -h');
  console.log('  Custom location: npm run backup:notes -- --output-dir /other/path\n');
}

// Main demo function
async function main() {
  const args = process.argv.slice(2);

  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
Backup System Demo

Usage: tsx scripts/backup-example.ts [options]

Options:
  --demo          Run full demonstration
  --usage         Show usage examples
  --best-practices Show best practices
  --troubleshoot  Show troubleshooting guide
  --help, -h      Show this help

Examples:
  tsx scripts/backup-example.ts --demo
  tsx scripts/backup-example.ts --usage
  tsx scripts/backup-example.ts --best-practices
    `);
    return;
  }

  if (args.includes('--demo')) {
    await demonstrateBackupSystem();
  }

  if (args.includes('--usage')) {
    await usageExamples();
  }

  if (args.includes('--best-practices')) {
    await bestPractices();
  }

  if (args.includes('--troubleshoot')) {
    await troubleshooting();
  }

  if (args.length === 0) {
    console.log('🎯 Backup and Restore System');
    console.log('Run with --help to see available options');
    console.log('Quick start: tsx scripts/backup-example.ts --demo');
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
