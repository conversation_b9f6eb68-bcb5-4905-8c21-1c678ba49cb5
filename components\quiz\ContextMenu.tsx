import React from 'react';
import { useQuiz } from './QuizContext';

interface ContextMenuProps {
  // No props needed as we'll use the context
}

const ContextMenu: React.FC<ContextMenuProps> = () => {
  const { 
    contextMenu, 
    translatedText, 
    handleTranslate, 
    displayLanguage 
  } = useQuiz();

  if (!contextMenu) return null;

  return (
    <div
      style={{
        position: 'fixed',
        top: contextMenu.y,
        left: contextMenu.x,
        zIndex: 1000
      }}
      className="bg-white shadow-md rounded p-2"
      onClick={(e) => e.stopPropagation()}
    >
      {translatedText ? (
        <div className="px-4 py-2">{translatedText}</div>
      ) : (
        <button
          onClick={handleTranslate}
          className="text-blue-500 hover:underline px-4 py-2"
        >
          {displayLanguage === 'en' ? 'Translate' : '翻译'}
        </button>
      )}
    </div>
  );
};

export default ContextMenu;
