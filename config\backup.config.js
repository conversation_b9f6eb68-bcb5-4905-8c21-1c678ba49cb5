/**
 * Backup Configuration
 * 
 * This file contains default settings for the backup and restore system.
 * You can override these settings using environment variables or command line arguments.
 */

module.exports = {
  // Default backup settings
  backup: {
    // Default output directory for backups
    outputDir: process.env.BACKUP_DIR || './backups',
    
    // Whether to include uploaded files by default
    includeFiles: process.env.BACKUP_INCLUDE_FILES !== 'false',
    
    // Whether to create database dump by default
    includeDatabaseDump: process.env.BACKUP_INCLUDE_DB_DUMP !== 'false',
    
    // Whether to compress backups by default
    compress: process.env.BACKUP_COMPRESS !== 'false',
    
    // Maximum number of backups to keep (0 = unlimited)
    maxBackups: parseInt(process.env.BACKUP_MAX_COUNT) || 30,
    
    // Maximum age of backups in days (0 = unlimited)
    maxAgeDays: parseInt(process.env.BACKUP_MAX_AGE_DAYS) || 90,
  },

  // Default restore settings
  restore: {
    // Whether to skip existing files/records by default
    skipExisting: process.env.RESTORE_SKIP_EXISTING !== 'false',
    
    // Whether to restore files by default
    restoreFiles: process.env.RESTORE_FILES !== 'false',
    
    // Whether to restore database by default
    restoreDatabase: process.env.RESTORE_DATABASE !== 'false',
  },

  // Database settings
  database: {
    // Database connection URL
    url: process.env.DATABASE_URL,
    
    // Connection timeout in milliseconds
    timeout: parseInt(process.env.DB_TIMEOUT) || 30000,
    
    // Whether to use SSL for database connections
    ssl: process.env.DB_SSL === 'true',
  },

  // Storage settings
  storage: {
    // Uploads directory
    uploadsDir: process.env.UPLOADS_DIR || './uploads',
    
    // Maximum file size to backup (in bytes, 0 = unlimited)
    maxFileSize: parseInt(process.env.BACKUP_MAX_FILE_SIZE) || 0,
    
    // File extensions to exclude from backup
    excludeExtensions: (process.env.BACKUP_EXCLUDE_EXTENSIONS || '').split(',').filter(Boolean),
  },

  // Logging settings
  logging: {
    // Log level (error, warn, info, debug)
    level: process.env.BACKUP_LOG_LEVEL || 'info',
    
    // Whether to log to file
    logToFile: process.env.BACKUP_LOG_TO_FILE === 'true',
    
    // Log file path
    logFile: process.env.BACKUP_LOG_FILE || './logs/backup.log',
  },

  // Notification settings (for future implementation)
  notifications: {
    // Whether to send notifications
    enabled: process.env.BACKUP_NOTIFICATIONS === 'true',
    
    // Email settings
    email: {
      enabled: process.env.BACKUP_EMAIL_NOTIFICATIONS === 'true',
      to: process.env.BACKUP_EMAIL_TO,
      from: process.env.BACKUP_EMAIL_FROM,
      smtp: {
        host: process.env.SMTP_HOST,
        port: parseInt(process.env.SMTP_PORT) || 587,
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS,
        },
      },
    },
    
    // Webhook settings
    webhook: {
      enabled: process.env.BACKUP_WEBHOOK_NOTIFICATIONS === 'true',
      url: process.env.BACKUP_WEBHOOK_URL,
      method: process.env.BACKUP_WEBHOOK_METHOD || 'POST',
    },
  },

  // Cleanup settings
  cleanup: {
    // Whether to automatically clean up old backups
    enabled: process.env.BACKUP_AUTO_CLEANUP === 'true',
    
    // Run cleanup after each backup
    runAfterBackup: process.env.BACKUP_CLEANUP_AFTER === 'true',
    
    // Cleanup schedule (cron format)
    schedule: process.env.BACKUP_CLEANUP_SCHEDULE || '0 3 * * 0', // Weekly at 3 AM
  },

  // Security settings
  security: {
    // Whether to encrypt backups
    encrypt: process.env.BACKUP_ENCRYPT === 'true',
    
    // Encryption key (should be set via environment variable)
    encryptionKey: process.env.BACKUP_ENCRYPTION_KEY,
    
    // Whether to verify backup integrity
    verifyIntegrity: process.env.BACKUP_VERIFY_INTEGRITY !== 'false',
  },

  // Performance settings
  performance: {
    // Number of concurrent file operations
    concurrency: parseInt(process.env.BACKUP_CONCURRENCY) || 5,
    
    // Buffer size for file operations (in bytes)
    bufferSize: parseInt(process.env.BACKUP_BUFFER_SIZE) || 64 * 1024, // 64KB
    
    // Whether to use compression for database dumps
    compressDbDump: process.env.BACKUP_COMPRESS_DB_DUMP !== 'false',
  },
};

/**
 * Environment Variables Reference:
 * 
 * BACKUP_DIR                    - Default backup output directory
 * BACKUP_INCLUDE_FILES          - Include uploaded files (true/false)
 * BACKUP_INCLUDE_DB_DUMP        - Include database dump (true/false)
 * BACKUP_COMPRESS               - Compress backups (true/false)
 * BACKUP_MAX_COUNT              - Maximum number of backups to keep
 * BACKUP_MAX_AGE_DAYS           - Maximum age of backups in days
 * 
 * RESTORE_SKIP_EXISTING         - Skip existing files/records (true/false)
 * RESTORE_FILES                 - Restore files (true/false)
 * RESTORE_DATABASE              - Restore database (true/false)
 * 
 * DATABASE_URL                  - Database connection URL
 * DB_TIMEOUT                    - Database connection timeout (ms)
 * DB_SSL                        - Use SSL for database (true/false)
 * 
 * UPLOADS_DIR                   - Uploads directory path
 * BACKUP_MAX_FILE_SIZE          - Maximum file size to backup (bytes)
 * BACKUP_EXCLUDE_EXTENSIONS     - File extensions to exclude (comma-separated)
 * 
 * BACKUP_LOG_LEVEL              - Log level (error/warn/info/debug)
 * BACKUP_LOG_TO_FILE            - Log to file (true/false)
 * BACKUP_LOG_FILE               - Log file path
 * 
 * BACKUP_NOTIFICATIONS          - Enable notifications (true/false)
 * BACKUP_EMAIL_NOTIFICATIONS    - Enable email notifications (true/false)
 * BACKUP_EMAIL_TO               - Email recipient
 * BACKUP_EMAIL_FROM             - Email sender
 * SMTP_HOST                     - SMTP server host
 * SMTP_PORT                     - SMTP server port
 * SMTP_SECURE                   - Use secure SMTP (true/false)
 * SMTP_USER                     - SMTP username
 * SMTP_PASS                     - SMTP password
 * 
 * BACKUP_WEBHOOK_NOTIFICATIONS  - Enable webhook notifications (true/false)
 * BACKUP_WEBHOOK_URL            - Webhook URL
 * BACKUP_WEBHOOK_METHOD         - Webhook HTTP method
 * 
 * BACKUP_AUTO_CLEANUP           - Enable automatic cleanup (true/false)
 * BACKUP_CLEANUP_AFTER          - Run cleanup after backup (true/false)
 * BACKUP_CLEANUP_SCHEDULE       - Cleanup schedule (cron format)
 * 
 * BACKUP_ENCRYPT                - Encrypt backups (true/false)
 * BACKUP_ENCRYPTION_KEY         - Encryption key
 * BACKUP_VERIFY_INTEGRITY       - Verify backup integrity (true/false)
 * 
 * BACKUP_CONCURRENCY            - Number of concurrent operations
 * BACKUP_BUFFER_SIZE            - Buffer size for file operations (bytes)
 * BACKUP_COMPRESS_DB_DUMP       - Compress database dumps (true/false)
 */
