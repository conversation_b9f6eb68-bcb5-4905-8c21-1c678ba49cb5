-- CreateEnum
CREATE TYPE "Role" AS ENUM ('PARENT', 'CHILD', 'TEACHER', 'ADMIN');

-- CreateEnum
CREATE TYPE "Status" AS ENUM ('ACTIVE', 'PENDING', 'INACTIVE');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "LicenseType" AS ENUM ('FREE_TRIAL', 'STANDARD_PLAN');

-- CreateTable
CREATE TABLE "TG_Account" (
    "id" SERIAL NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "role" "Role" NOT NULL,
    "parentId" INTEGER,
    "status" "Status" NOT NULL DEFAULT 'ACTIVE',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TG_Account_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TG_Parent" (
    "id" SERIAL NOT NULL,
    "fullName" TEXT NOT NULL,

    CONSTRAINT "TG_Parent_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TG_Child" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "year" TEXT NOT NULL,
    "username" TEXT NOT NULL,
    "pin" TEXT NOT NULL,
    "parentId" INTEGER NOT NULL,

    CONSTRAINT "TG_Child_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TG_License" (
    "id" SERIAL NOT NULL,
    "type" "LicenseType" NOT NULL,
    "duration" INTEGER,
    "accountId" INTEGER NOT NULL,

    CONSTRAINT "TG_License_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "TG_Account_email_key" ON "TG_Account"("email");

-- CreateIndex
CREATE UNIQUE INDEX "TG_Child_username_key" ON "TG_Child"("username");

-- CreateIndex
CREATE UNIQUE INDEX "TG_Child_pin_key" ON "TG_Child"("pin");

-- CreateIndex
CREATE UNIQUE INDEX "TG_License_accountId_key" ON "TG_License"("accountId");

-- AddForeignKey
ALTER TABLE "TG_Account" ADD CONSTRAINT "TG_Account_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES "TG_Parent"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TG_Child" ADD CONSTRAINT "TG_Child_parentId_fkey" FOREIGN KEY ("parentId") REFERENCES "TG_Parent"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TG_License" ADD CONSTRAINT "TG_License_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "TG_Account"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
