import { useState, useEffect } from 'react';
import { Language, QuestionType } from '@prisma/client';
import useSWR from 'swr';

// Define types
interface Batch {
  id: number;
  adminName: string;
  yearNumber: number;
  subjectName: string;
  unitNumber?: number;
  unitTopic?: string;
  questionTypes: QuestionType[];
  numQuestions: number;
  modelUsed: string;
  language: Language;
  status: string;
  createdAt: string;
  completedAt?: string;
  questionCount: number;
  noteCount: number;
  promptTokens?: number;
  completionTokens?: number;
  totalTokens?: number;
}

// Fetcher function for SWR
const fetcher = (url: string) => fetch(url).then(res => res.json());

interface BatchesTableProps {
  onRestartBatch: (batchId: number) => Promise<void>;
  isLoading: boolean;
}

export default function BatchesTable({ onRestartBatch, isLoading }: BatchesTableProps) {
  // Fetch batches using SWR
  const { data: batches, error: batchesError, mutate: refreshBatches } = useSWR<Batch[]>(
    '/api/admin/batches',
    fetcher,
    { refreshInterval: 10000 } // Refresh every 10 seconds
  );

  // Filter state
  const [filteredBatches, setFilteredBatches] = useState<Batch[]>([]);
  const [modelFilter, setModelFilter] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [dateFilter, setDateFilter] = useState<string>('');
  const [sortField, setSortField] = useState<string>('createdAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Get unique models for filter dropdown
  const uniqueModels = batches 
    ? [...new Set(batches.map(batch => batch.modelUsed))]
    : [];

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Apply filters and sorting
  useEffect(() => {
    if (!batches) return;

    let result = [...batches];

    // Apply model filter
    if (modelFilter) {
      result = result.filter(batch => batch.modelUsed === modelFilter);
    }

    // Apply status filter
    if (statusFilter) {
      result = result.filter(batch => batch.status === statusFilter);
    }

    // Apply date filter
    if (dateFilter) {
      const today = new Date();
      const startDate = new Date();
      
      if (dateFilter === 'today') {
        startDate.setHours(0, 0, 0, 0);
      } else if (dateFilter === 'week') {
        startDate.setDate(today.getDate() - 7);
      } else if (dateFilter === 'month') {
        startDate.setMonth(today.getMonth() - 1);
      }
      
      result = result.filter(batch => new Date(batch.createdAt) >= startDate);
    }

    // Apply sorting
    result.sort((a, b) => {
      let aValue: any = a[sortField as keyof Batch];
      let bValue: any = b[sortField as keyof Batch];
      
      // Handle null/undefined values
      if (aValue === undefined || aValue === null) aValue = sortDirection === 'asc' ? Infinity : -Infinity;
      if (bValue === undefined || bValue === null) bValue = sortDirection === 'asc' ? Infinity : -Infinity;
      
      // Compare based on sort direction
      if (sortDirection === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredBatches(result);
  }, [batches, modelFilter, statusFilter, dateFilter, sortField, sortDirection]);

  // Handle sort click
  const handleSortClick = (field: string) => {
    if (sortField === field) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new field and default to descending
      setSortField(field);
      setSortDirection('desc');
    }
  };

  // Render sort indicator
  const renderSortIndicator = (field: string) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? ' ↑' : ' ↓';
  };

  if (batchesError) return <div className="text-red-500">Error loading batches</div>;
  if (!batches) return <div className="text-gray-500">Loading batches...</div>;

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <h2 className="text-xl font-semibold mb-4">Generation Batches</h2>
      
      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Model</label>
          <select
            value={modelFilter}
            onChange={(e) => setModelFilter(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md"
          >
            <option value="">All Models</option>
            {uniqueModels.map((model) => (
              <option key={model} value={model}>{model}</option>
            ))}
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md"
          >
            <option value="">All Statuses</option>
            <option value="PENDING">Pending</option>
            <option value="IN_PROGRESS">In Progress</option>
            <option value="COMPLETED">Completed</option>
            <option value="FAILED">Failed</option>
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
          <select
            value={dateFilter}
            onChange={(e) => setDateFilter(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md"
          >
            <option value="">All Time</option>
            <option value="today">Today</option>
            <option value="week">Last 7 Days</option>
            <option value="month">Last 30 Days</option>
          </select>
        </div>
        
        <div className="flex items-end">
          <button
            onClick={() => {
              setModelFilter('');
              setStatusFilter('');
              setDateFilter('');
              setSortField('createdAt');
              setSortDirection('desc');
            }}
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
          >
            Reset Filters
          </button>
        </div>
      </div>
      
      {/* Batches Table */}
      {filteredBatches.length === 0 ? (
        <div className="text-gray-500 py-4">No batches found matching the filters</div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSortClick('id')}>
                  ID{renderSortIndicator('id')}
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSortClick('createdAt')}>
                  Created{renderSortIndicator('createdAt')}
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSortClick('status')}>
                  Status{renderSortIndicator('status')}
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Year/Subject
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSortClick('questionCount')}>
                  Questions{renderSortIndicator('questionCount')}
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Language
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSortClick('modelUsed')}>
                  Model{renderSortIndicator('modelUsed')}
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSortClick('totalTokens')}>
                  Tokens{renderSortIndicator('totalTokens')}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredBatches.map((batch) => (
                <tr key={batch.id} className="hover:bg-gray-50">
                  <td className="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{batch.id}</td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatDate(batch.createdAt)}</td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm">
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        batch.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                        batch.status === 'FAILED' ? 'bg-red-100 text-red-800' :
                        batch.status === 'IN_PROGRESS' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-blue-100 text-blue-800'
                      }`}>
                        {batch.status}
                      </span>
                      {batch.status === 'FAILED' && (
                        <button
                          onClick={() => onRestartBatch(batch.id)}
                          disabled={isLoading}
                          className="px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 text-xs font-medium"
                          title="Restart failed batch"
                        >
                          Restart
                        </button>
                      )}
                    </div>
                  </td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                    Year {batch.yearNumber} / {batch.subjectName}
                    {batch.unitNumber && <span> / Unit {batch.unitNumber}</span>}
                  </td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                    {batch.questionCount} / {batch.numQuestions}
                  </td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      batch.language === 'ZH' ? 'bg-red-100 text-red-800' :
                      batch.language === 'EN' ? 'bg-blue-100 text-blue-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {batch.language === 'ZH' ? 'Chinese' :
                       batch.language === 'EN' ? 'English' :
                       batch.language === 'MS' ? 'Malay' : batch.language}
                    </span>
                  </td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{batch.modelUsed}</td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                    {batch.totalTokens ? (
                      <div className="text-xs">
                        <div>Total: {batch.totalTokens.toLocaleString()}</div>
                        <div>Input: {batch.promptTokens?.toLocaleString()}</div>
                        <div>Output: {batch.completionTokens?.toLocaleString()}</div>
                      </div>
                    ) : (
                      <span className="text-gray-400">-</span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}
