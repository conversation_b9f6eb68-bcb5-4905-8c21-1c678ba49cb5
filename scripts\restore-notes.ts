#!/usr/bin/env tsx

import fs from 'fs/promises';
import path from 'path';
import { execSync } from 'child_process';
import prisma from '../lib/prisma';

interface BackupMetadata {
  timestamp: string;
  version: string;
  notesCount: number;
  filesCount: number;
  totalFileSize: number;
  databaseUrl: string;
}

interface RestoreOptions {
  backupPath: string;
  restoreFiles?: boolean;
  restoreDatabase?: boolean;
  skipExisting?: boolean;
  dryRun?: boolean;
}

interface NotesData {
  notes: any[];
  years: any[];
  subjects: any[];
  units: any[];
  exportedAt: string;
  exportVersion: string;
}

class NotesRestoreService {
  private uploadsDir: string;

  constructor() {
    this.uploadsDir = path.join(process.cwd(), 'uploads');
  }

  /**
   * Restore notes and files from a backup
   */
  async restoreBackup(options: RestoreOptions): Promise<void> {
    const {
      backupPath,
      restoreFiles = true,
      restoreDatabase = true,
      skipExisting = true,
      dryRun = false
    } = options;

    console.log(`🔄 Starting restore from: ${backupPath}`);
    
    if (dryRun) {
      console.log('🧪 DRY RUN MODE - No changes will be made');
    }

    try {
      // 1. Validate and extract backup if needed
      const extractedPath = await this.prepareBackup(backupPath);
      
      // 2. Load and validate backup metadata
      const metadata = await this.loadBackupMetadata(extractedPath);
      console.log(`📊 Backup info: ${metadata.notesCount} notes, ${metadata.filesCount} files`);
      console.log(`📅 Created: ${new Date(metadata.timestamp).toLocaleString()}`);

      // 3. Restore database records
      if (restoreDatabase) {
        console.log('📊 Restoring database records...');
        await this.restoreNotesData(extractedPath, { skipExisting, dryRun });
      }

      // 4. Restore uploaded files
      if (restoreFiles) {
        console.log('📁 Restoring uploaded files...');
        await this.restoreUploadedFiles(extractedPath, { skipExisting, dryRun });
      }

      // 5. Cleanup temporary extraction if needed
      if (extractedPath !== backupPath) {
        if (!dryRun) {
          await fs.rm(extractedPath, { recursive: true });
        }
        console.log('🧹 Cleaned up temporary files');
      }

      console.log('✅ Restore completed successfully!');

    } catch (error) {
      console.error('❌ Restore failed:', error);
      throw error;
    }
  }

  /**
   * Prepare backup for restoration (extract if compressed)
   */
  private async prepareBackup(backupPath: string): Promise<string> {
    const stats = await fs.stat(backupPath);
    
    if (stats.isDirectory()) {
      console.log('📁 Using backup directory directly');
      return backupPath;
    }

    if (backupPath.endsWith('.tar.gz') || backupPath.endsWith('.tgz')) {
      console.log('📦 Extracting compressed backup...');
      
      const tempDir = path.join(path.dirname(backupPath), `temp-restore-${Date.now()}`);
      await fs.mkdir(tempDir, { recursive: true });

      try {
        execSync(`tar -xzf "${backupPath}" -C "${tempDir}"`, {
          stdio: 'inherit'
        });

        // Find the extracted directory
        const contents = await fs.readdir(tempDir);
        const extractedDir = contents.find(item => item.startsWith('notes-backup-'));
        
        if (!extractedDir) {
          throw new Error('Could not find backup directory in extracted archive');
        }

        const extractedPath = path.join(tempDir, extractedDir);
        console.log(`✅ Extracted to: ${extractedPath}`);
        
        return extractedPath;

      } catch (error) {
        await fs.rm(tempDir, { recursive: true });
        throw error;
      }
    }

    throw new Error('Unsupported backup format. Expected directory or .tar.gz file');
  }

  /**
   * Load and validate backup metadata
   */
  private async loadBackupMetadata(backupPath: string): Promise<BackupMetadata> {
    const metadataPath = path.join(backupPath, 'backup-metadata.json');
    
    try {
      const metadataContent = await fs.readFile(metadataPath, 'utf8');
      const metadata: BackupMetadata = JSON.parse(metadataContent);
      
      // Validate required fields
      if (!metadata.timestamp || !metadata.notesCount === undefined) {
        throw new Error('Invalid backup metadata');
      }

      return metadata;

    } catch (error) {
      throw new Error(`Failed to load backup metadata: ${error}`);
    }
  }

  /**
   * Restore notes data to database
   */
  private async restoreNotesData(
    backupPath: string, 
    options: { skipExisting: boolean; dryRun: boolean }
  ): Promise<void> {
    const notesDataPath = path.join(backupPath, 'notes-data.json');
    
    try {
      const notesDataContent = await fs.readFile(notesDataPath, 'utf8');
      const notesData: NotesData = JSON.parse(notesDataContent);

      console.log(`  📋 Processing ${notesData.notes.length} notes...`);

      if (options.dryRun) {
        console.log('  🧪 DRY RUN: Would restore notes data');
        return;
      }

      let restoredCount = 0;
      let skippedCount = 0;

      for (const note of notesData.notes) {
        try {
          // Check if note already exists
          if (options.skipExisting) {
            const existing = await prisma.note.findFirst({
              where: {
                filename: note.filename,
                fileUrl: note.fileUrl,
                yearId: note.yearId,
                subjectId: note.subjectId,
                unitId: note.unitId
              }
            });

            if (existing) {
              skippedCount++;
              continue;
            }
          }

          // Ensure related records exist
          await this.ensureRelatedRecords(note, notesData);

          // Create the note record
          await prisma.note.create({
            data: {
              filename: note.filename,
              fileUrl: note.fileUrl,
              mimeType: note.mimeType,
              fileSize: note.fileSize,
              contentType: note.contentType,
              yearId: note.yearId,
              subjectId: note.subjectId,
              unitId: note.unitId,
              createdAt: new Date(note.createdAt)
            }
          });

          restoredCount++;

          if (restoredCount % 10 === 0) {
            console.log(`    📄 Restored ${restoredCount} notes...`);
          }

        } catch (error) {
          console.warn(`    ⚠️  Failed to restore note ${note.filename}:`, error);
        }
      }

      console.log(`  ✅ Restored ${restoredCount} notes, skipped ${skippedCount}`);

    } catch (error) {
      throw new Error(`Failed to restore notes data: ${error}`);
    }
  }

  /**
   * Ensure related records (year, subject, unit) exist
   */
  private async ensureRelatedRecords(note: any, notesData: NotesData): Promise<void> {
    // Find related records from backup data
    const year = notesData.years.find(y => y.id === note.yearId);
    const subject = notesData.subjects.find(s => s.id === note.subjectId);
    const unit = notesData.units.find(u => u.id === note.unitId);

    if (!year || !subject || !unit) {
      throw new Error(`Missing related records for note ${note.filename}`);
    }

    // Ensure year exists
    await prisma.year.upsert({
      where: { id: year.id },
      update: {},
      create: {
        id: year.id,
        yearNumber: year.yearNumber
      }
    });

    // Ensure subject exists
    await prisma.subject.upsert({
      where: { id: subject.id },
      update: {},
      create: {
        id: subject.id,
        name: subject.name
      }
    });

    // Ensure unit exists
    await prisma.unit.upsert({
      where: { id: unit.id },
      update: {},
      create: {
        id: unit.id,
        unitNumber: unit.unitNumber,
        topicEn: unit.topicEn,
        topicZh: unit.topicZh,
        topicMs: unit.topicMs,
        subjectId: unit.subjectId,
        yearId: unit.yearId
      }
    });
  }

  /**
   * Restore uploaded files
   */
  private async restoreUploadedFiles(
    backupPath: string,
    options: { skipExisting: boolean; dryRun: boolean }
  ): Promise<void> {
    const filesBackupDir = path.join(backupPath, 'uploads');

    try {
      await fs.access(filesBackupDir);
    } catch (error) {
      console.log('  ℹ️  No files directory in backup, skipping file restore');
      return;
    }

    // Ensure uploads directory exists
    if (!options.dryRun) {
      await fs.mkdir(this.uploadsDir, { recursive: true });
    }

    const files = await fs.readdir(filesBackupDir);
    console.log(`  📁 Processing ${files.length} files...`);

    if (options.dryRun) {
      console.log('  🧪 DRY RUN: Would restore uploaded files');
      return;
    }

    let restoredCount = 0;
    let skippedCount = 0;

    for (const file of files) {
      const sourcePath = path.join(filesBackupDir, file);
      const destPath = path.join(this.uploadsDir, file);

      try {
        // Check if file already exists
        if (options.skipExisting) {
          try {
            await fs.access(destPath);
            skippedCount++;
            continue;
          } catch {
            // File doesn't exist, proceed with restore
          }
        }

        await fs.copyFile(sourcePath, destPath);
        restoredCount++;

        if (restoredCount % 10 === 0) {
          console.log(`    📄 Restored ${restoredCount} files...`);
        }

      } catch (error) {
        console.warn(`    ⚠️  Failed to restore file ${file}:`, error);
      }
    }

    console.log(`  ✅ Restored ${restoredCount} files, skipped ${skippedCount}`);
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0 || args.includes('--help')) {
    console.log(`
Usage: npm run restore:notes <backup-path> [options]

Arguments:
  backup-path       Path to backup directory or .tar.gz file

Options:
  --no-files        Skip restoring uploaded files
  --no-database     Skip restoring database records
  --overwrite       Overwrite existing records/files (default: skip)
  --dry-run         Show what would be restored without making changes
  --help           Show this help message

Examples:
  npm run restore:notes ./backups/notes-backup-2024-01-15T10-30-00-000Z
  npm run restore:notes ./backups/notes-backup-2024-01-15T10-30-00-000Z.tar.gz
  npm run restore:notes backup.tar.gz -- --dry-run
  npm run restore:notes backup.tar.gz -- --no-files --overwrite
    `);
    process.exit(0);
  }

  const backupPath = args[0];
  const options: RestoreOptions = {
    backupPath,
    restoreFiles: true,
    restoreDatabase: true,
    skipExisting: true,
    dryRun: false
  };

  // Parse command line arguments
  for (let i = 1; i < args.length; i++) {
    switch (args[i]) {
      case '--no-files':
        options.restoreFiles = false;
        break;
      case '--no-database':
        options.restoreDatabase = false;
        break;
      case '--overwrite':
        options.skipExisting = false;
        break;
      case '--dry-run':
        options.dryRun = true;
        break;
    }
  }

  try {
    const restoreService = new NotesRestoreService();
    await restoreService.restoreBackup(options);
    process.exit(0);
  } catch (error) {
    console.error('Restore failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

export { NotesRestoreService };
