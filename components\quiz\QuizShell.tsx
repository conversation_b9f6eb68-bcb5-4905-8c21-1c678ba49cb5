import React, { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import { QuizProvider, useQuiz } from './QuizContext';
import useQuizData from './useQuizData';
import Loader from './Loader';
import QuizHeader from './QuizHeader';
import QuestionRenderer from './QuestionRenderer';
import AiTutorPanel from './AiTutorPanel';
import AiTutorAvatar from './AiTutorAvatar';
import ContextMenu from './ContextMenu';
import QuizResults from './QuizResults';

// Inner component that uses the QuizContext
const QuizContent: React.FC = () => {
  const router = useRouter();
  const {
    questions,
    results,
    isFinished,
    setQuestions,
    setQuizAttemptId,
    setCurrentIndex,
    setQuizType,
    setIsFinished,
    displayLanguage,
    setContextMenu,
    quizAttemptId,
    currentIndex,
    isAiTutorCollapsed,
    aiTutorWidthRatio,
    setAiTutorWidthRatio,
    setIsAiTutorCollapsed,
    toggleAiTutor,
    setChildQuizLanguage,
    setChildMenuLanguage,
    unreadMessages
  } = useQuiz();
  const { data, error, isLoading, quizType } = useQuizData(router.query.attemptId);
  const { status } = useSession();

  // Set up quiz data when it's loaded
  useEffect(() => {
    if (data) {
      console.log('QuizShell: Setting up quiz data with currentIndex:', data.quizAttempt.currentQuestionIndex);
      console.log('QuizShell: Quiz completion status:', data.isCompleted ? 'Completed' : 'Active');

      // Set quiz data in context
      setQuestions(data.questions || []);
      setQuizAttemptId(data.quizAttempt.id);
      setCurrentIndex(data.quizAttempt.currentQuestionIndex);
      setQuizType(data.quizAttempt.quizType || quizType);      // Set child language preferences if available
      if (data.childLanguagePreferences?.quizLanguage && data.childLanguagePreferences?.menuLanguage) {
        console.log('QuizShell: Setting child language preferences:', data.childLanguagePreferences);
        
        // Ensure consistent case handling by converting to uppercase
        const quizLang = data.childLanguagePreferences.quizLanguage.toUpperCase() as 'EN' | 'ZH' | 'MS';
        const menuLang = data.childLanguagePreferences.menuLanguage.toUpperCase() as 'EN' | 'ZH' | 'MS';
        
        setChildQuizLanguage(quizLang);
        setChildMenuLanguage(menuLang);

        // Store language preferences in localStorage for other components to access
        try {
          localStorage.setItem('childQuizLanguage', quizLang);
          localStorage.setItem('childMenuLanguage', menuLang);
        } catch (error) {
          console.error('Error saving language preferences to localStorage:', error);
        }
      }

      // If the quiz is completed, set isFinished to true to show the completion screen
      if (data.isCompleted) {
        setIsFinished(true);      }      
      
      // Check for config in metadata
      if (data.quizAttempt?.metadata?.configSnapshot) {
        console.log('QuizShell: Found quiz config:', data.quizAttempt.metadata.configSnapshot);

        // Store config settings in localStorage for other components to access
        try {
          const attemptId = data.quizAttempt.id;
          const config = data.quizAttempt.metadata.configSnapshot;
          localStorage.setItem(`quiz_${attemptId}_allowTranslate`, String(!!config.allowTranslate));
          localStorage.setItem(`quiz_${attemptId}_allowHints`, String(!!config.allowHints));
          localStorage.setItem(`quiz_${attemptId}_allowAiTutor`, String(!!config.allowAiTutor));

          // Apply config settings
          if (config.allowAiTutor === false) {
            // Force collapse AI tutor if not allowed
            setIsAiTutorCollapsed(true);
          }
        } catch (error) {
          console.error('Error saving config to localStorage:', error);
        }
      }
    }
  }, [data, setQuestions, setQuizAttemptId, setCurrentIndex, setQuizType, setIsFinished, quizType, setIsAiTutorCollapsed, setChildQuizLanguage, setChildMenuLanguage]);

  // Add a visibility change listener to track tab switching
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        console.log('Tab is now visible, current question index:', currentIndex);
      } else {
        console.log('Tab is now hidden, current question index:', currentIndex);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [currentIndex]);

  // Mark quiz as completed when finished
  useEffect(() => {
    // Only run this effect when the quiz is finished and we have a valid quizAttemptId
    // And don't run if the quiz was already completed (data.isCompleted is true)
    if (isFinished && quizAttemptId !== null && !data?.isCompleted) {
      const markQuizAsCompleted = async () => {
        try {
          console.log("Marking quiz as completed, quizAttemptId:", quizAttemptId);
          // Calculate score based on results
          const score = results.filter(r => r && r.isCorrect === true).length;
          const scorePercentage = (score / questions.length) * 100;

          // Call the API to explicitly mark the quiz as completed
          const completeResponse = await fetch('/api/quiz/complete', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify({
              quizAttemptId: quizAttemptId,
              score: scorePercentage,
              totalQuestions: questions.length,
            }),
          });

          if (!completeResponse.ok) {
            throw new Error(`Error marking quiz as completed: ${completeResponse.statusText}`);
          }

          console.log("Quiz successfully marked as completed");
        } catch (error) {
          console.error('Error in quiz completion process:', error);
        }
      };

      markQuizAsCompleted();
    }
  }, [isFinished, quizAttemptId, results, questions.length, data?.isCompleted]);

  // Finished screen
  if (isFinished) {
    const score = results.filter(r => r && r.isCorrect === true).length;
    const total = questions.length;
    const alreadyCompleted = data?.isCompleted || false;

    return (
      <QuizResults
        score={score}
        total={total}
        displayLanguage={displayLanguage}
        onBack={() => router.push('/student-dashboard')}
        alreadyCompleted={alreadyCompleted}
      />
    );
  }

  // Handle loading state
  if (status === 'loading' || isLoading) {
    return <Loader message="Loading Quiz..." />;
  }

  // If authenticated but questions haven't loaded yet (or attemptId was missing)
  if (status === 'authenticated' && (questions.length === 0 || error)) {
    // Check if attemptId exists; if not, maybe redirect or show error
    if (!router.query.attemptId) {
      return (
        <div className="min-h-screen bg-blue-600 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-lg p-6 max-w-sm w-full text-center">
            <p className="text-xl mb-4">Error: Quiz Attempt ID is missing.</p>
            <button
              onClick={() => router.push('/student-dashboard')}
              className="bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-4 rounded focus:outline-none"
            >
              {displayLanguage === 'en' ? 'Back to Dashboard' : '返回仪表板'}
            </button>
          </div>
        </div>
      );
    }

    // If attemptId exists but there was an error loading data
    if (error) {
      return (
        <div className="min-h-screen bg-blue-600 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-lg p-6 max-w-sm w-full text-center">
            <p className="text-xl mb-4">Error loading quiz data.</p>
            <p className="mb-4">{error.message}</p>
            <button
              onClick={() => router.push('/student-dashboard')}
              className="bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-4 rounded focus:outline-none"
            >
              {displayLanguage === 'en' ? 'Back to Dashboard' : '返回仪表板'}
            </button>
          </div>
        </div>
      );
    }

    // If attemptId exists but data is still loading
    return <Loader message="Loading Quiz Data..." />;
  }

  // If somehow rendering while unauthenticated (should be redirected, but as fallback)
  if (status !== 'authenticated') {
    return (
      <div className="min-h-screen bg-blue-600 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-lg p-6 max-w-sm w-full text-center">
          <p className="text-xl mb-4">Redirecting to login...</p>
          <div className="loader mx-auto mb-4 w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          <button
            onClick={() => router.push('/login')}
            className="bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-4 rounded focus:outline-none mt-4"
          >
            {displayLanguage === 'en' ? 'Go to Login' : '前往登录'}
          </button>
        </div>
      </div>
    );
  }

  // Main quiz view (only renders if authenticated and data is loaded)
  return (
    <div
      className="min-h-screen bg-blue-600 flex items-center justify-center p-1 sm:p-2 md:p-3"
      onClick={() => setContextMenu(null)}
    >
      <div className="bg-blue-400 rounded-lg shadow-lg p-3 sm:p-4 md:p-5 max-w-full w-full h-[calc(100vh-2rem)] text-white relative flex flex-col md:flex-row">
        {/* Left Column: Question, Choices/Input, Next Button, Indicators */}
        <div
          className={`${isAiTutorCollapsed ? 'w-full' : 'md:w-[40%]'} transition-all duration-300 ease-in-out overflow-y-auto`}
          style={{ flex: isAiTutorCollapsed ? '1' : `${1 - aiTutorWidthRatio}`, maxHeight: 'calc(100vh - 4rem)' }}
        >
          <QuizHeader />
          <QuestionRenderer />
        </div>

        {/* AI Tutor Toggle Button (visible when collapsed and allowed) */}
        {isAiTutorCollapsed && questions.length > 0 && currentIndex < questions.length && (
          <>
            {quizAttemptId && localStorage.getItem(`quiz_${quizAttemptId}_allowAiTutor`) !== 'false' && (
              <button
                onClick={toggleAiTutor}
                className="absolute right-4 bottom-4 flex items-center justify-center shadow-lg hover:shadow-xl transition-all z-10 transform hover:scale-105 relative"
                title={displayLanguage === 'en' ? 'Show AI Tutor' : '显示AI导师'}
              >
                <AiTutorAvatar size="lg" />
                {/* Notification badge for unread messages */}
                {unreadMessages > 0 && (
                  <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center shadow-sm">
                    {unreadMessages > 9 ? '9+' : unreadMessages}
                  </div>
                )}
              </button>
            )}
          </>
        )}

        {/* Resizable Divider */}
        {!isAiTutorCollapsed && (
          <div
            className="w-2 cursor-col-resize bg-blue-500 hover:bg-blue-600 transition-colors"
            onMouseDown={(e) => {
              e.preventDefault();

              const startX = e.clientX;
              const startWidth = e.currentTarget.previousElementSibling?.getBoundingClientRect().width || 0;
              const containerWidth = e.currentTarget.parentElement?.getBoundingClientRect().width || 0;

              const onMouseMove = (moveEvent: MouseEvent) => {
                const deltaX = moveEvent.clientX - startX;
                const newWidth = Math.max(0.2, Math.min(0.8, (startWidth + deltaX) / containerWidth));
                const aiTutorRatio = 1 - newWidth;
                setAiTutorWidthRatio(aiTutorRatio);
              };

              const onMouseUp = () => {
                document.removeEventListener('mousemove', onMouseMove);
                document.removeEventListener('mouseup', onMouseUp);
              };

              document.addEventListener('mousemove', onMouseMove);
              document.addEventListener('mouseup', onMouseUp);
            }}
          />
        )}

        {/* Right Column: AI Tutor Section */}
        {!isAiTutorCollapsed && questions.length > 0 && currentIndex < questions.length && (
          <div
            className="relative transition-all duration-300 ease-in-out overflow-hidden flex flex-col"
            style={{ flex: aiTutorWidthRatio, maxHeight: 'calc(100vh - 4rem)' }}
          >
            <button
              onClick={toggleAiTutor}
              className="absolute right-3 top-3 bg-white text-blue-600 rounded-full w-8 h-8 flex items-center justify-center shadow-lg hover:bg-blue-50 transition-all z-10 transform hover:scale-105"
              title={displayLanguage === 'en' ? 'Hide AI Tutor' : '隐藏AI导师'}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            <AiTutorPanel
              displayLanguage={displayLanguage}
              visible={true}
            />
          </div>
        )}
      </div>

      {/* Context Menu for translations */}
      <ContextMenu />
    </div>
  );
};

// Outer component that provides the QuizContext
const QuizShell: React.FC = () => {
  const router = useRouter();
  const { status } = useSession();

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  // Only render the QuizProvider and QuizContent when we have authentication status
  if (status === 'loading') {
    return <Loader message="Loading..." />;
  }

  return (
    <QuizProvider>
      <QuizContent />
    </QuizProvider>
  );
};

export default QuizShell;
