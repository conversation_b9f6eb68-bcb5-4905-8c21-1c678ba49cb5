import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import fs from 'fs/promises';
import path from 'path';
import { NotesBackupService } from '../scripts/backup-notes';
import { NotesRestoreService } from '../scripts/restore-notes';
import prisma from '../lib/prisma';

describe('Backup and Restore System', () => {
  const testBackupDir = path.join(process.cwd(), 'test-backups');
  const testUploadsDir = path.join(process.cwd(), 'test-uploads');
  
  beforeAll(async () => {
    // Create test directories
    await fs.mkdir(testBackupDir, { recursive: true });
    await fs.mkdir(testUploadsDir, { recursive: true });
  });

  afterAll(async () => {
    // Cleanup test directories
    try {
      await fs.rm(testBackupDir, { recursive: true });
      await fs.rm(testUploadsDir, { recursive: true });
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  beforeEach(async () => {
    // Clean up any existing test data
    try {
      const files = await fs.readdir(testBackupDir);
      for (const file of files) {
        await fs.rm(path.join(testBackupDir, file), { recursive: true });
      }
    } catch (error) {
      // Ignore if directory is empty
    }
  });

  describe('NotesBackupService', () => {
    it('should create a backup with metadata', async () => {
      const backupService = new NotesBackupService(testBackupDir);
      
      const backupPath = await backupService.createBackup({
        includeFiles: false,
        includeDatabaseDump: false,
        compress: false
      });

      // Check that backup directory was created
      const stats = await fs.stat(backupPath);
      expect(stats.isDirectory()).toBe(true);

      // Check that metadata file exists
      const metadataPath = path.join(backupPath, 'backup-metadata.json');
      const metadataExists = await fs.access(metadataPath).then(() => true).catch(() => false);
      expect(metadataExists).toBe(true);

      // Check metadata content
      const metadataContent = await fs.readFile(metadataPath, 'utf8');
      const metadata = JSON.parse(metadataContent);
      
      expect(metadata).toHaveProperty('timestamp');
      expect(metadata).toHaveProperty('version');
      expect(metadata).toHaveProperty('notesCount');
      expect(typeof metadata.notesCount).toBe('number');
    });

    it('should create notes data export', async () => {
      const backupService = new NotesBackupService(testBackupDir);
      
      const backupPath = await backupService.createBackup({
        includeFiles: false,
        includeDatabaseDump: false,
        compress: false
      });

      // Check that notes data file exists
      const notesDataPath = path.join(backupPath, 'notes-data.json');
      const notesDataExists = await fs.access(notesDataPath).then(() => true).catch(() => false);
      expect(notesDataExists).toBe(true);

      // Check notes data structure
      const notesDataContent = await fs.readFile(notesDataPath, 'utf8');
      const notesData = JSON.parse(notesDataContent);
      
      expect(notesData).toHaveProperty('notes');
      expect(notesData).toHaveProperty('years');
      expect(notesData).toHaveProperty('subjects');
      expect(notesData).toHaveProperty('units');
      expect(notesData).toHaveProperty('exportedAt');
      expect(notesData).toHaveProperty('exportVersion');
      
      expect(Array.isArray(notesData.notes)).toBe(true);
      expect(Array.isArray(notesData.years)).toBe(true);
      expect(Array.isArray(notesData.subjects)).toBe(true);
      expect(Array.isArray(notesData.units)).toBe(true);
    });

    it('should handle file backup when uploads directory exists', async () => {
      // Create test files
      const testFile1 = path.join(testUploadsDir, 'test1.txt');
      const testFile2 = path.join(testUploadsDir, 'test2.pdf');
      
      await fs.writeFile(testFile1, 'Test content 1');
      await fs.writeFile(testFile2, 'Test content 2');

      // Mock the uploads directory by temporarily changing the service
      const backupService = new NotesBackupService(testBackupDir);
      
      // We can't easily test file copying without modifying the service
      // This test verifies the backup creation doesn't fail when files exist
      const backupPath = await backupService.createBackup({
        includeFiles: false, // Skip files for this test
        includeDatabaseDump: false,
        compress: false
      });

      expect(backupPath).toBeDefined();
      
      // Cleanup test files
      await fs.unlink(testFile1);
      await fs.unlink(testFile2);
    });

    it('should create compressed backup when requested', async () => {
      const backupService = new NotesBackupService(testBackupDir);
      
      const backupPath = await backupService.createBackup({
        includeFiles: false,
        includeDatabaseDump: false,
        compress: true
      });

      // Check that compressed file was created
      expect(backupPath.endsWith('.tar.gz')).toBe(true);
      
      const stats = await fs.stat(backupPath);
      expect(stats.isFile()).toBe(true);
    });
  });

  describe('NotesRestoreService', () => {
    let testBackupPath: string;

    beforeEach(async () => {
      // Create a test backup for restore tests
      const backupService = new NotesBackupService(testBackupDir);
      testBackupPath = await backupService.createBackup({
        includeFiles: false,
        includeDatabaseDump: false,
        compress: false
      });
    });

    it('should validate backup structure', async () => {
      const restoreService = new NotesRestoreService();
      
      // This should not throw an error for a valid backup
      await expect(
        restoreService.restoreBackup({
          backupPath: testBackupPath,
          restoreFiles: false,
          restoreDatabase: false,
          dryRun: true
        })
      ).resolves.not.toThrow();
    });

    it('should perform dry run without making changes', async () => {
      const restoreService = new NotesRestoreService();
      
      // Get initial note count
      const initialCount = await prisma.note.count();
      
      await restoreService.restoreBackup({
        backupPath: testBackupPath,
        restoreFiles: false,
        restoreDatabase: true,
        dryRun: true
      });

      // Count should be unchanged after dry run
      const finalCount = await prisma.note.count();
      expect(finalCount).toBe(initialCount);
    });

    it('should handle missing backup files gracefully', async () => {
      const restoreService = new NotesRestoreService();
      
      await expect(
        restoreService.restoreBackup({
          backupPath: '/nonexistent/path',
          dryRun: true
        })
      ).rejects.toThrow();
    });

    it('should handle compressed backup files', async () => {
      // Create a compressed backup
      const backupService = new NotesBackupService(testBackupDir);
      const compressedBackupPath = await backupService.createBackup({
        includeFiles: false,
        includeDatabaseDump: false,
        compress: true
      });

      const restoreService = new NotesRestoreService();
      
      // This should not throw an error for a valid compressed backup
      await expect(
        restoreService.restoreBackup({
          backupPath: compressedBackupPath,
          restoreFiles: false,
          restoreDatabase: false,
          dryRun: true
        })
      ).resolves.not.toThrow();
    });
  });

  describe('Integration Tests', () => {
    it('should create and restore a backup successfully', async () => {
      // Skip this test if we don't have a test database
      if (!process.env.DATABASE_URL?.includes('test')) {
        console.log('Skipping integration test - not using test database');
        return;
      }

      const backupService = new NotesBackupService(testBackupDir);
      const restoreService = new NotesRestoreService();

      // Create backup
      const backupPath = await backupService.createBackup({
        includeFiles: false,
        includeDatabaseDump: false,
        compress: false
      });

      // Verify backup was created
      expect(backupPath).toBeDefined();
      
      const stats = await fs.stat(backupPath);
      expect(stats.isDirectory()).toBe(true);

      // Test restore in dry run mode
      await expect(
        restoreService.restoreBackup({
          backupPath,
          restoreFiles: false,
          restoreDatabase: true,
          dryRun: true
        })
      ).resolves.not.toThrow();
    });
  });

  describe('Error Handling', () => {
    it('should handle database connection errors gracefully', async () => {
      // Temporarily modify DATABASE_URL to cause connection error
      const originalUrl = process.env.DATABASE_URL;
      process.env.DATABASE_URL = 'postgresql://invalid:invalid@localhost:9999/invalid';

      const backupService = new NotesBackupService(testBackupDir);
      
      await expect(
        backupService.createBackup({
          includeFiles: false,
          includeDatabaseDump: false,
          compress: false
        })
      ).rejects.toThrow();

      // Restore original URL
      process.env.DATABASE_URL = originalUrl;
    });

    it('should cleanup on backup failure', async () => {
      const backupService = new NotesBackupService('/invalid/path/that/cannot/be/created');
      
      await expect(
        backupService.createBackup({
          includeFiles: false,
          includeDatabaseDump: false,
          compress: false
        })
      ).rejects.toThrow();

      // Verify no partial backup directories were left behind
      // (This would need to be implemented in the actual service)
    });
  });
});
